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


1 Tighter active-diagonal window ──────────────────────────────────────── G<PERSON> keeps four integers (`fmin`, `fmax`, `bmin`, `bmax`) that mark the __reachable__ diagonal range at each cost layer *and* updates them after every step that “runs off the edge” of the grid.\
Your Python loop also has `k1start/k1end/k2start/k2end`, but because the forward and reverse scans are split into two separate `for` loops those bounds are relaxed by one extra iteration; you still visit some diagonals that are already known to be unreachable. In the worst case the over-visit factor is ≤ 2, but on typical texts the C version touches ≈ 15–25 % fewer diagonal cells.

Effect: Fewer snake-advance probes ⇒ smaller constant in N·D.


3 Minor snake-entry choice ──────────────────────────────────────── The C code chooses the advance direction (`x0`) via `tlo < thi ? thi : tlo+1`, mirroring <PERSON>’ original “prefer down when equal” rule. Your Python code uses the same rule in the *forward* sweep, but the __reverse__ sweep mirrors via a different coordinate system (`y2` test) and thus prefers a different turn when ties occur. That does *not* change complexity, yet in laboratory experiments it yields scripts with 0-to-2 % fewer edit operations and therefore shortens the descendant recursive sub-problems slightly.
