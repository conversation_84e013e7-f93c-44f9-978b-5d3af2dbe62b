"""
Numba-optimized core functions for Myers diff algorithm.

This module provides JIT-compiled versions of the core Myers diff functions
for improved performance on large datasets.
"""

from numba import njit, type
import numpy as np
import time 
# Constants for diff operations
DIFF_DELETE = -1
DIFF_INSERT = 1
DIFF_EQUAL = 0

@njit
def diff_bisect_numba(text1, text2):
    """
    Numba-optimized version of diff_bisect.
    
    Find the 'middle snake' of a diff, split the problem in two
    and return the recursively constructed diff.
    See <PERSON> 1986 paper: An O(ND) Difference Algorithm and Its Variations.
    
    Args:
        text1: First sequence (as numpy array or numba List)
        text2: Second sequence (as numpy array or numba List)
        
    Returns:
        Tuple containing (x1, y1) coordinates of the middle snake endpoint,
        or (-1, -1) if no snake found within max_d iterations
    """
    # Cache the list lengths to prevent multiple calls.
    text1_length = len(text1)
    text2_length = len(text2)
    max_d = (text1_length + text2_length + 1) // 2
    v_offset = max_d
    v_length = 2 * max_d
    
    # Initialize arrays
    v1 = np.full(v_length, -1, dtype=np.int32)
    v1[v_offset + 1] = 0
    v2 = v1.copy()
    
    delta = text1_length - text2_length
    # If the total number of elements is odd, then the front path will
    # collide with the reverse path.
    front = (delta % 2 != 0)
    
    # Offsets for start and end of k loop.
    # Prevents mapping of space beyond the grid.
    k1start = 0
    k1end = 0
    k2start = 0
    k2end = 0
    
    for d in range(max_d):
        # Walk the front path one step.
        k1_range_start = -d + k1start
        k1_range_end = d + 1 - k1end
        
        k1 = k1_range_start
        while k1 < k1_range_end:
            k1_offset = v_offset + k1
            if k1 == -d or (k1 != d and v1[k1_offset - 1] < v1[k1_offset + 1]):
                x1 = v1[k1_offset + 1]
            else:
                x1 = v1[k1_offset - 1] + 1
            y1 = x1 - k1
            
            while (x1 < text1_length and y1 < text2_length and text1[x1] == text2[y1]):
                x1 += 1
                y1 += 1
                
            v1[k1_offset] = x1
            
            if x1 > text1_length:
                # Ran off the right of the graph.
                k1end += 2
            elif y1 > text2_length:
                # Ran off the bottom of the graph.
                k1start += 2
            elif front:
                k2_offset = v_offset + delta - k1
                if k2_offset >= 0 and k2_offset < v_length and v2[k2_offset] != -1:
                    # Mirror x2 onto top-left coordinate system.
                    x2 = text1_length - v2[k2_offset]
                    if x1 >= x2:
                        # Overlap detected.
                        return (x1, y1)
            
            k1 += 2

        # Walk the reverse path one step.
        k2_range_start = -d + k2start
        k2_range_end = d + 1 - k2end
        
        k2 = k2_range_start
        while k2 < k2_range_end:
            k2_offset = v_offset + k2
            if k2 == -d or (k2 != d and v2[k2_offset - 1] < v2[k2_offset + 1]):
                x2 = v2[k2_offset + 1]
            else:
                x2 = v2[k2_offset - 1] + 1
            y2 = x2 - k2
            
            while (x2 < text1_length and y2 < text2_length and 
                   text1[text1_length - x2 - 1] == text2[text2_length - y2 - 1]):
                x2 += 1
                y2 += 1
                
            v2[k2_offset] = x2
            
            if x2 > text1_length:
                # Ran off the left of the graph.
                k2end += 2
            elif y2 > text2_length:
                # Ran off the top of the graph.
                k2start += 2
            elif not front:
                k1_offset = v_offset + delta - k2
                if k1_offset >= 0 and k1_offset < v_length and v1[k1_offset] != -1:
                    x1 = v1[k1_offset]
                    y1 = v_offset + x1 - k1_offset
                    # Mirror x2 onto top-left coordinate system.
                    x2 = text1_length - x2
                    if x1 >= x2:
                        # Overlap detected.
                        return (x1, y1)
            
            k2 += 2

    # Diff took too long and hit the deadline or
    # number of diffs equals number of elements, no commonality at all.
    return (-1, -1)


# Wrapper function that integrates with the main class
def diff_bisect_optimized(self, text1, text2):
    """
    Wrapper function for the Numba-optimized diff_bisect.
    
    This function converts the input to numpy arrays, calls the optimized
    version, and handles the result integration with the class methods.
    """
    # Convert inputs to numpy arrays for Numba compatibility
    # No hashing needed - inputs are already hash numbers
    text1_array = np.array(text1)
    text2_array = np.array(text2)
    
    # Call the optimized function
    start = time.time()
    result = diff_bisect_numba(text1_array, text2_array)
    end = time.time()
    print ("Numba diff_bisect took", end - start, "seconds")
    # Handle the result
    if result[0] == -1 and result[1] == -1:
        # No snake found - return the simple diff
        return [(self.DIFF_DELETE, text1), (self.DIFF_INSERT, text2)]
    else:
        # Snake found - call the split function
        x, y = result
        return self.diff_bisectSplit(text1, text2, x, y)