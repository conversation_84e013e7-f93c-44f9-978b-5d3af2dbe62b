#!/usr/bin/env python3
"""
Test script to demonstrate the eager compilation of Numba functions.

This script shows the difference between:
1. Pre-compiled Numba functions (eager compilation)
2. JIT compilation on first call (lazy compilation)
"""

import time
import numpy as np
import os

def test_without_precompilation():
    """Test Numba function without pre-compilation (disable it first)."""
    print("=== Testing WITHOUT pre-compilation ===")
    
    # Disable pre-compilation
    os.environ['DISABLE_NUMBA_PRECOMPILE'] = '1'
    
    # Import the module (this should skip pre-compilation)
    import importlib
    import sys
    if 'myers_numba_core' in sys.modules:
        importlib.reload(sys.modules['myers_numba_core'])
    else:
        import myers_numba_core
    
    # Create test data
    text1 = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], dtype=np.int32)
    text2 = np.array([1, 2, 4, 5, 6, 8, 9, 10, 11], dtype=np.int32)
    
    # Time the first call (should include JIT compilation)
    print("First call (includes JIT compilation):")
    start = time.time()
    result1 = myers_numba_core.diff_bisect_numba(text1, text2)
    end = time.time()
    first_call_time = end - start
    print(f"  Result: {result1}")
    print(f"  Time: {first_call_time:.6f} seconds")
    
    # Time the second call (should be fast, already compiled)
    print("Second call (already compiled):")
    start = time.time()
    result2 = myers_numba_core.diff_bisect_numba(text1, text2)
    end = time.time()
    second_call_time = end - start
    print(f"  Result: {result2}")
    print(f"  Time: {second_call_time:.6f} seconds")
    
    print(f"Compilation overhead: {first_call_time - second_call_time:.6f} seconds")
    return first_call_time, second_call_time

def test_with_precompilation():
    """Test Numba function with pre-compilation enabled."""
    print("\n=== Testing WITH pre-compilation ===")
    
    # Enable pre-compilation
    if 'DISABLE_NUMBA_PRECOMPILE' in os.environ:
        del os.environ['DISABLE_NUMBA_PRECOMPILE']
    
    # Import the module (this should trigger pre-compilation)
    import importlib
    import sys
    if 'myers_numba_core' in sys.modules:
        importlib.reload(sys.modules['myers_numba_core'])
    else:
        import myers_numba_core
    
    # Create test data
    text1 = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10], dtype=np.int32)
    text2 = np.array([1, 2, 4, 5, 6, 8, 9, 10, 11], dtype=np.int32)
    
    # Time the first call (should be fast, already pre-compiled)
    print("First call (pre-compiled):")
    start = time.time()
    result1 = myers_numba_core.diff_bisect_numba(text1, text2)
    end = time.time()
    first_call_time = end - start
    print(f"  Result: {result1}")
    print(f"  Time: {first_call_time:.6f} seconds")
    
    # Time the second call
    print("Second call:")
    start = time.time()
    result2 = myers_numba_core.diff_bisect_numba(text1, text2)
    end = time.time()
    second_call_time = end - start
    print(f"  Result: {result2}")
    print(f"  Time: {second_call_time:.6f} seconds")
    
    return first_call_time, second_call_time

def main():
    """Run the comparison test."""
    print("Numba Eager Compilation Test")
    print("=" * 50)
    
    # Test without pre-compilation
    without_times = test_without_precompilation()
    
    # Test with pre-compilation
    with_times = test_with_precompilation()
    
    # Summary
    print("\n=== SUMMARY ===")
    print(f"Without pre-compilation:")
    print(f"  First call:  {without_times[0]:.6f} seconds (includes JIT compilation)")
    print(f"  Second call: {without_times[1]:.6f} seconds")
    print(f"  JIT overhead: {without_times[0] - without_times[1]:.6f} seconds")
    
    print(f"\nWith pre-compilation:")
    print(f"  First call:  {with_times[0]:.6f} seconds (pre-compiled)")
    print(f"  Second call: {with_times[1]:.6f} seconds")
    
    speedup = without_times[0] / with_times[0]
    print(f"\nSpeedup for first call: {speedup:.2f}x")
    
    if without_times[0] > with_times[0]:
        print("✅ Pre-compilation successfully eliminates JIT overhead!")
    else:
        print("⚠️  Pre-compilation may not be working as expected.")

if __name__ == "__main__":
    main()
